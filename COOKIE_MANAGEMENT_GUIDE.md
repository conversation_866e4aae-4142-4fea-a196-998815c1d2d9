# Cookie Management System - Developer Guide

This guide explains how to use the centralized cookie management system in the Flash application.

## Overview

The centralized cookie management system provides:

- **Security-first approach** with automatic security flags
- **Consent-based operations** with automatic verification
- **Centralized registry** for all application cookies
- **React hooks** for easy component integration
- **Comprehensive logging** for debugging and compliance

## Quick Start

### 1. Setting Cookies

```typescript
import { setFunctionalCookie, setEssentialCookie } from '@/lib/cookie-utils';

// Set a functional cookie (requires user consent)
const success = setFunctionalCookie('user_preference', 'dark_mode');

// Set an essential cookie (bypasses consent)
const success = setEssentialCookie('session_id', 'abc123');
```

### 2. Reading Cookies

```typescript
import { getCookie } from '@/lib/cookie-utils';

const userPreference = getCookie('user_preference');
if (userPreference) {
  console.log('User prefers:', userPreference);
}
```

### 3. Using React Hook

```typescript
import { useCookie } from '@/lib/cookie-utils';

function MyComponent() {
  const [theme, setTheme, removeTheme] = useCookie('theme_preference', 'light');
  
  const handleThemeChange = (newTheme: string) => {
    const success = setTheme(newTheme);
    if (!success) {
      console.log('Cookie blocked due to consent settings');
    }
  };
  
  return (
    <div>
      <p>Current theme: {theme || 'default'}</p>
      <button onClick={() => handleThemeChange('dark')}>
        Switch to Dark
      </button>
    </div>
  );
}
```

## Cookie Registry

All cookies must be registered in the central registry (`src/lib/cookie-utils.ts`):

```typescript
export const COOKIE_REGISTRY: Record<string, CookieDefinition> = {
  my_new_cookie: {
    name: 'my_new_cookie',
    category: 'functional', // 'essential' | 'functional' | 'analytics' | 'marketing'
    purpose: 'Stores user interface preferences',
    maxAge: 60 * 60 * 24 * 30, // 30 days in seconds
    secure: true,
    httpOnly: false, // Set to true if cookie should not be accessible via JavaScript
    sameSite: 'Strict', // 'Strict' | 'Lax' | 'None'
    description: 'Detailed description for privacy policy'
  }
};
```

### Cookie Categories

- **Essential**: Required for basic app functionality (always allowed)
- **Functional**: Enhance user experience (requires consent)
- **Analytics**: Track usage and performance (requires consent)
- **Marketing**: Advertising and tracking (requires consent)

## Advanced Usage

### 1. Direct Cookie Manager Usage

```typescript
import { CookieManager } from '@/lib/cookie-utils';

// Set with custom options
CookieManager.set('custom_cookie', 'value', {
  maxAge: 3600, // 1 hour
  path: '/admin',
  secure: true
});

// Check if cookie exists
if (CookieManager.exists('custom_cookie')) {
  console.log('Cookie exists');
}

// Get all cookies
const allCookies = CookieManager.getAll();
console.log('All cookies:', allCookies);
```

### 2. Consent Integration

```typescript
import { CookieManager } from '@/lib/cookie-utils';

// Clear non-essential cookies when consent is withdrawn
CookieManager.clearNonEssentialCookies();

// Get cookie inventory for privacy policy
const inventory = CookieManager.getCookieInventory();
inventory.forEach(cookie => {
  console.log(`${cookie.name}: ${cookie.purpose}`);
});
```

### 3. Error Handling

```typescript
import { setFunctionalCookie } from '@/lib/cookie-utils';

const success = setFunctionalCookie('user_setting', 'value');
if (!success) {
  // Cookie was blocked due to:
  // - Lack of user consent
  // - Server-side execution
  // - Other error (check console logs)
  console.log('Failed to set cookie');
}
```

## Security Features

### Automatic Security Flags

All cookies automatically include:

- **Secure**: Only transmitted over HTTPS
- **SameSite=Strict**: Prevents CSRF attacks
- **Path=/**: Scoped to entire application
- **Proper encoding**: Values are URL-encoded

### Consent Verification

Before setting any non-essential cookie:

1. System checks cookie category in registry
2. Verifies user consent for that category
3. Blocks cookie if consent not granted
4. Logs action for compliance auditing

## Migration from Legacy Code

### Before (Vulnerable)

```typescript
// Old direct document.cookie usage
document.cookie = `user_pref=${value}; path=/; max-age=86400`;

// Reading cookies manually
function getCookieValue(name: string) {
  const cookies = document.cookie.split(';');
  for (const cookie of cookies) {
    const [cookieName, cookieValue] = cookie.trim().split('=');
    if (cookieName === name) {
      return cookieValue;
    }
  }
  return null;
}
```

### After (Secure)

```typescript
// New centralized system
import { setFunctionalCookie, getCookie } from '@/lib/cookie-utils';

// Setting cookies
setFunctionalCookie('user_pref', value);

// Reading cookies
const userPref = getCookie('user_pref');
```

## Best Practices

### 1. Always Register Cookies

```typescript
// ❌ Don't use unregistered cookies
CookieManager.set('random_cookie', 'value'); // Logs warning

// ✅ Register first, then use
// Add to COOKIE_REGISTRY, then:
setFunctionalCookie('registered_cookie', 'value');
```

### 2. Choose Appropriate Categories

```typescript
// ✅ Essential for app functionality
setEssentialCookie('csrf_token', token);

// ✅ Functional for user preferences
setFunctionalCookie('theme', 'dark');

// ✅ Use appropriate category
// Don't mark analytics cookies as essential
```

### 3. Handle Consent Changes

```typescript
import { useCookieConsentCleanup } from '@/lib/cookie-utils';

function MyComponent() {
  // Automatically cleans up cookies when consent changes
  useCookieConsentCleanup();
  
  // Component logic...
}
```

### 4. Error Handling

```typescript
const success = setFunctionalCookie('setting', 'value');
if (!success) {
  // Provide fallback behavior
  localStorage.setItem('setting', 'value'); // Fallback to localStorage
}
```

## Debugging

### 1. Console Logging

All cookie operations are logged:

```
[CookieManager] Cookie set successfully: { name: 'sidebar_state', category: 'functional' }
[CookieManager] Cookie blocked due to lack of consent: theme_preference
```

### 2. Cookie Inventory

```typescript
import { CookieManager } from '@/lib/cookie-utils';

// Debug current cookies
console.log('Current cookies:', CookieManager.getAll());

// Debug registered cookies
console.log('Registered cookies:', CookieManager.getCookieInventory());
```

### 3. Consent Status

```typescript
import { getCookieConsent } from '@/lib/cookie-consent';

const consent = getCookieConsent();
console.log('Consent status:', consent);
```

## Testing

### Unit Tests

```typescript
import { CookieManager } from '@/lib/cookie-utils';

// Mock document.cookie for testing
Object.defineProperty(document, 'cookie', {
  writable: true,
  value: ''
});

test('should set cookie with security flags', () => {
  CookieManager.set('test_cookie', 'value');
  expect(document.cookie).toContain('Secure');
  expect(document.cookie).toContain('SameSite=Strict');
});
```

### Integration Tests

```typescript
import { render, screen } from '@testing-library/react';
import { useCookie } from '@/lib/cookie-utils';

function TestComponent() {
  const [value, setValue] = useCookie('test_cookie');
  return <div>{value}</div>;
}

test('should read cookie value', () => {
  render(<TestComponent />);
  // Test cookie reading behavior
});
```

## Troubleshooting

### Common Issues

1. **Cookie not being set**
   - Check user consent status
   - Verify cookie is registered in registry
   - Check browser console for error logs

2. **Cookie not persisting**
   - Ensure HTTPS is enabled for Secure cookies
   - Check maxAge/expires settings
   - Verify SameSite compatibility

3. **Server-side errors**
   - Cookie operations only work client-side
   - Use Next.js `cookies()` for server-side reading

### Support

For technical issues:
1. Check browser console for detailed error logs
2. Verify consent status with `getCookieConsent()`
3. Review cookie registry configuration
4. Test with different consent settings

The centralized cookie management system provides a secure, compliant foundation for all cookie operations in the application.
