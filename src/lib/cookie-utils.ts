/**
 * Centralized Cookie Management System
 * Provides secure, consistent cookie operations with built-in consent verification
 */

import * as React from 'react';
import { getCookieConsent, isFunctionalEnabled } from './cookie-consent';
import { logger } from './logger';

// Cookie Security Configuration
export interface CookieOptions {
  path?: string;
  domain?: string;
  maxAge?: number; // in seconds
  expires?: Date;
  secure?: boolean;
  httpOnly?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
}

// Default security settings
const DEFAULT_COOKIE_OPTIONS: Required<Pick<CookieOptions, 'path' | 'secure' | 'sameSite'>> = {
  path: '/',
  secure: typeof window !== 'undefined' ? window.location.protocol === 'https:' : true,
  sameSite: 'Strict'
};

// Cookie Categories for consent management
export type CookieCategory = 'essential' | 'functional' | 'analytics' | 'marketing';

// Cookie Registry - Central inventory of all cookies used in the application
export interface CookieDefinition {
  name: string;
  category: CookieCategory;
  purpose: string;
  maxAge: number;
  secure: boolean;
  httpOnly: boolean;
  sameSite: 'Strict' | 'Lax' | 'None';
  description: string;
}

// Central Cookie Registry
export const COOKIE_REGISTRY: Record<string, CookieDefinition> = {
  sidebar_state: {
    name: 'sidebar_state',
    category: 'functional',
    purpose: 'Stores sidebar expanded/collapsed state for user interface preferences',
    maxAge: 60 * 60 * 24 * 7, // 7 days
    secure: true,
    httpOnly: false, // Needs to be accessible by client-side JavaScript
    sameSite: 'Strict',
    description: 'Remembers whether the sidebar is expanded or collapsed across sessions'
  },
  // Add more cookies as they are identified
  theme_preference: {
    name: 'theme_preference',
    category: 'functional',
    purpose: 'Stores user theme preference (light/dark mode)',
    maxAge: 60 * 60 * 24 * 365, // 1 year
    secure: true,
    httpOnly: false,
    sameSite: 'Strict',
    description: 'Remembers user theme preference across sessions'
  }
};

/**
 * Cookie Management Class
 * Provides centralized, secure cookie operations with consent verification
 */
export class CookieManager {
  /**
   * Set a cookie with security best practices and consent verification
   */
  static set(
    name: string, 
    value: string, 
    options: CookieOptions = {},
    bypassConsent: boolean = false
  ): boolean {
    try {
      // Server-side safety check
      if (typeof window === 'undefined') {
        logger.warn('[CookieManager] Attempted to set cookie on server-side:', name);
        return false;
      }

      // Get cookie definition from registry
      const cookieDefinition = COOKIE_REGISTRY[name];
      if (!cookieDefinition && !bypassConsent) {
        logger.warn('[CookieManager] Cookie not found in registry:', name);
        // Allow setting but log warning for unregistered cookies
      }

      // Consent verification (unless bypassed for essential cookies)
      if (!bypassConsent && cookieDefinition) {
        const hasConsent = this.checkConsent(cookieDefinition.category);
        if (!hasConsent) {
          logger.info('[CookieManager] Cookie blocked due to lack of consent:', name);
          return false;
        }
      }

      // Merge options with defaults and registry definition
      const finalOptions: CookieOptions = {
        ...DEFAULT_COOKIE_OPTIONS,
        ...(cookieDefinition ? {
          maxAge: cookieDefinition.maxAge,
          secure: cookieDefinition.secure,
          sameSite: cookieDefinition.sameSite
        } : {}),
        ...options
      };

      // Build cookie string
      const cookieString = this.buildCookieString(name, value, finalOptions);
      
      // Set the cookie
      document.cookie = cookieString;
      
      logger.info('[CookieManager] Cookie set successfully:', { name, category: cookieDefinition?.category });
      return true;

    } catch (error) {
      logger.error('[CookieManager] Error setting cookie:', { name, error });
      return false;
    }
  }

  /**
   * Get a cookie value
   */
  static get(name: string): string | null {
    try {
      if (typeof window === 'undefined') {
        return null;
      }

      const cookies = document.cookie.split(';');
      
      for (const cookie of cookies) {
        const [cookieName, cookieValue] = cookie.trim().split('=');
        if (cookieName === name) {
          return decodeURIComponent(cookieValue || '');
        }
      }
      
      return null;
    } catch (error) {
      logger.error('[CookieManager] Error getting cookie:', { name, error });
      return null;
    }
  }

  /**
   * Delete a cookie
   */
  static delete(name: string): boolean {
    try {
      if (typeof window === 'undefined') {
        return false;
      }

      // Set cookie with past expiration date
      const cookieString = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; Secure; SameSite=Strict`;
      document.cookie = cookieString;
      
      logger.info('[CookieManager] Cookie deleted:', name);
      return true;
    } catch (error) {
      logger.error('[CookieManager] Error deleting cookie:', { name, error });
      return false;
    }
  }

  /**
   * Check if a cookie exists
   */
  static exists(name: string): boolean {
    return this.get(name) !== null;
  }

  /**
   * Get all cookies as an object
   */
  static getAll(): Record<string, string> {
    try {
      if (typeof window === 'undefined') {
        return {};
      }

      const cookies: Record<string, string> = {};
      const cookieString = document.cookie;
      
      if (!cookieString) {
        return cookies;
      }

      cookieString.split(';').forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        if (name && value) {
          cookies[name] = decodeURIComponent(value);
        }
      });

      return cookies;
    } catch (error) {
      logger.error('[CookieManager] Error getting all cookies:', error);
      return {};
    }
  }

  /**
   * Clear all non-essential cookies based on current consent
   */
  static clearNonEssentialCookies(): void {
    try {
      const consent = getCookieConsent();
      const allCookies = this.getAll();

      Object.keys(allCookies).forEach(cookieName => {
        const cookieDefinition = COOKIE_REGISTRY[cookieName];
        
        if (cookieDefinition && cookieDefinition.category !== 'essential') {
          const hasConsent = this.checkConsent(cookieDefinition.category);
          if (!hasConsent) {
            this.delete(cookieName);
            logger.info('[CookieManager] Cleared non-essential cookie:', cookieName);
          }
        }
      });
    } catch (error) {
      logger.error('[CookieManager] Error clearing non-essential cookies:', error);
    }
  }

  /**
   * Get cookie inventory for privacy policy or debugging
   */
  static getCookieInventory(): CookieDefinition[] {
    return Object.values(COOKIE_REGISTRY);
  }

  /**
   * Check consent for a specific cookie category
   */
  private static checkConsent(category: CookieCategory): boolean {
    switch (category) {
      case 'essential':
        return true; // Essential cookies always allowed
      case 'functional':
        return isFunctionalEnabled();
      case 'analytics':
        const consent = getCookieConsent();
        return consent.analytics;
      case 'marketing':
        // Add marketing consent check when implemented
        return false;
      default:
        return false;
    }
  }

  /**
   * Build cookie string with all security attributes
   */
  private static buildCookieString(name: string, value: string, options: CookieOptions): string {
    let cookieString = `${name}=${encodeURIComponent(value)}`;

    if (options.path) {
      cookieString += `; path=${options.path}`;
    }

    if (options.domain) {
      cookieString += `; domain=${options.domain}`;
    }

    if (options.maxAge !== undefined) {
      cookieString += `; max-age=${options.maxAge}`;
    }

    if (options.expires) {
      cookieString += `; expires=${options.expires.toUTCString()}`;
    }

    if (options.secure) {
      cookieString += '; Secure';
    }

    if (options.httpOnly) {
      cookieString += '; HttpOnly';
    }

    if (options.sameSite) {
      cookieString += `; SameSite=${options.sameSite}`;
    }

    return cookieString;
  }
}

/**
 * Convenience functions for common cookie operations
 */

/**
 * Set a functional cookie (requires functional consent)
 */
export function setFunctionalCookie(name: string, value: string, options?: CookieOptions): boolean {
  return CookieManager.set(name, value, options);
}

/**
 * Set an essential cookie (bypasses consent)
 */
export function setEssentialCookie(name: string, value: string, options?: CookieOptions): boolean {
  return CookieManager.set(name, value, options, true);
}

/**
 * Get cookie value with error handling
 */
export function getCookie(name: string): string | null {
  return CookieManager.get(name);
}

/**
 * Delete cookie with error handling
 */
export function deleteCookie(name: string): boolean {
  return CookieManager.delete(name);
}

/**
 * Hook for React components to listen to cookie consent changes and clear cookies accordingly
 */
export function useCookieConsentCleanup(): void {
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleConsentChange = () => {
        CookieManager.clearNonEssentialCookies();
      };

      window.addEventListener('cookieConsentChanged', handleConsentChange);

      // Cleanup function
      return () => {
        window.removeEventListener('cookieConsentChanged', handleConsentChange);
      };
    }
  }, []);
}

/**
 * React hook for managing cookies with automatic consent verification
 */
export function useCookie(name: string, defaultValue: string = ''): [string | null, (value: string) => boolean, () => boolean] {
  const [cookieValue, setCookieValue] = React.useState<string | null>(null);

  // Initialize cookie value on mount
  React.useEffect(() => {
    const value = getCookie(name);
    setCookieValue(value);
  }, [name]);

  // Listen for consent changes that might affect this cookie
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleConsentChange = () => {
        const value = getCookie(name);
        setCookieValue(value);
      };

      window.addEventListener('cookieConsentChanged', handleConsentChange);

      return () => {
        window.removeEventListener('cookieConsentChanged', handleConsentChange);
      };
    }
  }, [name]);

  const updateCookie = React.useCallback((value: string): boolean => {
    const success = setFunctionalCookie(name, value);
    if (success) {
      setCookieValue(value);
    }
    return success;
  }, [name]);

  const removeCookie = React.useCallback((): boolean => {
    const success = deleteCookie(name);
    if (success) {
      setCookieValue(null);
    }
    return success;
  }, [name]);

  return [cookieValue, updateCookie, removeCookie];
}
