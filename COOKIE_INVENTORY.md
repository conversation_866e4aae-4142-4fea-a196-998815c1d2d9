# Cookie Inventory & Management Documentation

This document provides a comprehensive overview of all cookies used in the Flash application, their purposes, security configurations, and compliance information.

## Cookie Management Architecture

The application uses a centralized cookie management system (`src/lib/cookie-utils.ts`) that provides:

- **Consent-based cookie setting** with automatic verification
- **Security-first approach** with built-in security flags
- **Central registry** for all application cookies
- **Automatic cleanup** of non-consented cookies
- **Comprehensive logging** for debugging and compliance

## Cookie Categories

### Essential Cookies
Required for basic application functionality. These cookies are always set regardless of user consent.

| Cookie Name | Purpose | Duration | Security |
|-------------|---------|----------|----------|
| *None currently* | - | - | - |

### Functional Cookies
Enhance user experience by remembering preferences and settings. Require user consent.

| Cookie Name | Purpose | Duration | Security |
|-------------|---------|----------|----------|
| `sidebar_state` | Stores sidebar expanded/collapsed state | 7 days | Secure, SameSite=Strict |
| `theme_preference` | Stores user theme preference (light/dark) | 1 year | Secure, SameSite=Strict |

### Analytics Cookies
Used for understanding user behavior and improving the application. Require user consent.

| Cookie Name | Purpose | Duration | Security | Provider |
|-------------|---------|----------|----------|----------|
| `_ga` | Google Analytics main cookie | 2 years | Secure | Google |
| `_ga_*` | Google Analytics property-specific | 2 years | Secure | Google |
| `_gid` | Google Analytics session identifier | 24 hours | Secure | Google |

### Marketing Cookies
Currently not implemented in the application.

## Cookie Registry Configuration

All cookies are defined in the central registry (`src/lib/cookie-utils.ts`):

```typescript
export const COOKIE_REGISTRY: Record<string, CookieDefinition> = {
  sidebar_state: {
    name: 'sidebar_state',
    category: 'functional',
    purpose: 'Stores sidebar expanded/collapsed state for user interface preferences',
    maxAge: 60 * 60 * 24 * 7, // 7 days
    secure: true,
    httpOnly: false, // Needs to be accessible by client-side JavaScript
    sameSite: 'Strict',
    description: 'Remembers whether the sidebar is expanded or collapsed across sessions'
  }
  // Additional cookies defined here...
};
```

## Security Configuration

### Default Security Settings
All cookies set through the centralized system include these security measures:

- **Secure Flag**: Only transmitted over HTTPS connections
- **SameSite=Strict**: Prevents cross-site request forgery attacks
- **Path=/**: Scoped to the entire application
- **Proper Encoding**: All values are URL-encoded to prevent injection

### Cookie-Specific Security

#### Functional Cookies
- **Client-Side Accessible**: `httpOnly: false` (required for JavaScript access)
- **Strict SameSite**: Maximum CSRF protection
- **Secure Flag**: HTTPS-only transmission

#### Analytics Cookies (Third-Party)
- **Google Analytics**: Managed by Google with their security standards
- **Conditional Loading**: Only loaded with explicit user consent
- **Privacy-Compliant**: Configured for GDPR compliance

## Consent Management Integration

### Consent Categories
The cookie system integrates with the consent management system:

```typescript
export type CookieCategory = 'essential' | 'functional' | 'analytics' | 'marketing';
```

### Consent Verification
Before setting any non-essential cookie, the system:

1. Checks the cookie's category in the registry
2. Verifies user consent for that category
3. Blocks cookie setting if consent is not granted
4. Logs the action for compliance auditing

### Automatic Cleanup
When consent is withdrawn:

1. System identifies all cookies in the affected category
2. Automatically deletes non-consented cookies
3. Prevents future cookies in that category until consent is re-granted

## Usage Examples

### Setting Cookies

```typescript
import { CookieManager, setFunctionalCookie } from '@/lib/cookie-utils';

// Using the centralized manager
CookieManager.set('sidebar_state', 'expanded');

// Using convenience function
setFunctionalCookie('theme_preference', 'dark');
```

### Reading Cookies

```typescript
import { getCookie } from '@/lib/cookie-utils';

const sidebarState = getCookie('sidebar_state');
const theme = getCookie('theme_preference');
```

### Deleting Cookies

```typescript
import { deleteCookie } from '@/lib/cookie-utils';

deleteCookie('sidebar_state');
```

## Compliance Information

### GDPR Compliance
- **Consent Required**: All non-essential cookies require explicit user consent
- **Granular Control**: Users can consent to specific cookie categories
- **Easy Withdrawal**: Consent can be withdrawn at any time
- **Automatic Cleanup**: Non-consented cookies are automatically removed

### Data Retention
- **Functional Cookies**: 7 days to 1 year depending on purpose
- **Analytics Cookies**: Managed by Google Analytics (up to 2 years)
- **Consent Records**: Stored in localStorage with timestamp

### Cross-Border Data Transfer
- **Google Analytics**: Data may be transferred to Google servers globally
- **Functional Cookies**: Stored locally in user's browser only

## Monitoring and Debugging

### Logging
All cookie operations are logged with:
- Cookie name and category
- Success/failure status
- Consent verification results
- Error details when applicable

### Cookie Inventory Access
Get current cookie inventory programmatically:

```typescript
import { CookieManager } from '@/lib/cookie-utils';

const inventory = CookieManager.getCookieInventory();
console.log('All registered cookies:', inventory);
```

### Debug Information
View all current cookies:

```typescript
const allCookies = CookieManager.getAll();
console.log('Current cookies:', allCookies);
```

## Migration from Legacy Implementation

### Before (Direct document.cookie)
```typescript
// Old vulnerable implementation
document.cookie = `sidebar_state=${state}; path=/; max-age=604800`;
```

### After (Centralized Management)
```typescript
// New secure implementation
import { setFunctionalCookie } from '@/lib/cookie-utils';
setFunctionalCookie('sidebar_state', state);
```

## Future Enhancements

### Planned Features
1. **Server-Side Cookie Reading**: Integration with Next.js `cookies()` function
2. **Cookie Encryption**: For sensitive functional cookies
3. **Advanced Analytics**: Cookie usage analytics and optimization
4. **Marketing Cookie Support**: When marketing features are added

### Security Roadmap
1. **HttpOnly for Sensitive Data**: Implement server-side only cookies for sensitive information
2. **Cookie Signing**: Add integrity verification for critical cookies
3. **Advanced SameSite Policies**: Fine-tune SameSite settings per cookie
4. **Content Security Policy**: Integrate with CSP headers for additional protection

## Support and Maintenance

### Adding New Cookies
1. Define cookie in `COOKIE_REGISTRY`
2. Specify category, security settings, and purpose
3. Use centralized functions to set/get cookie
4. Update this documentation

### Troubleshooting
- Check browser console for cookie operation logs
- Verify consent status for functional/analytics cookies
- Ensure HTTPS is enabled for Secure cookies
- Check SameSite compatibility for cross-site scenarios

For technical support or questions about cookie implementation, refer to the centralized cookie management system in `src/lib/cookie-utils.ts`.
